(function() {
    const vscode = acquireVsCodeApi();
    
    const messagesDiv = document.getElementById('messages');
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    
    let currentAssistantMessage = null;
    let isStreaming = false;

    // Initialize Python bridge
    vscode.postMessage({ command: 'initialize' });

    function addMessage(message, sender) {
        // Handle different message types
        switch(sender) {
            case 'stream_start':
                // Start a new streaming message
                currentAssistantMessage = document.createElement('div');
                currentAssistantMessage.className = 'message assistant-message';
                currentAssistantMessage.textContent = '';
                messagesDiv.appendChild(currentAssistantMessage);
                isStreaming = true;
                sendButton.disabled = true;
                sendButton.textContent = 'Generating...';
                break;
                
            case 'stream_update':
                // Update the existing streaming message
                if (currentAssistantMessage) {
                    currentAssistantMessage.textContent = message;
                }
                // No change to isStreaming, sendButton state here, as it's handled by stream_start/assistant
                break;
                
            case 'assistant':
                // This is the final complete message
                if (currentAssistantMessage) {
                    currentAssistantMessage.textContent = message;
                } else {
                    // Fallback for non-streaming assistant messages (shouldn't happen if stream_start is used)
                    const assistantDiv = document.createElement('div');
                    assistantDiv.className = 'message assistant-message';
                    assistantDiv.textContent = message;
                    messagesDiv.appendChild(assistantDiv);
                }
                currentAssistantMessage = null; // Reset for next message
                isStreaming = false;
                sendButton.disabled = false;
                sendButton.textContent = 'Send';
                break;
                
            case 'thinking':
                // Show thinking indicators temporarily
                const thinkingDiv = document.createElement('div');
                thinkingDiv.className = 'message system-message thinking';
                thinkingDiv.textContent = message;
                thinkingDiv.style.fontStyle = 'italic';
                thinkingDiv.style.opacity = '0.7';
                thinkingDiv.style.fontSize = '0.9em';
                messagesDiv.appendChild(thinkingDiv);
                
                // Remove thinking indicator after a few seconds
                setTimeout(() => {
                    if (thinkingDiv.parentNode) {
                        thinkingDiv.parentNode.removeChild(thinkingDiv);
                    }
                }, 5000);
                break;
                
            case 'user':
                // User messages
                const userDiv = document.createElement('div');
                userDiv.className = 'message user-message';
                userDiv.textContent = message;
                messagesDiv.appendChild(userDiv);
                break;
                
            case 'system':
                // System messages
                const systemDiv = document.createElement('div');
                systemDiv.className = 'message system-message';
                systemDiv.textContent = message;
                systemDiv.style.fontSize = '0.9em';
                systemDiv.style.opacity = '0.8';
                messagesDiv.appendChild(systemDiv);
                break;
                
            case 'error':
                // Error messages
                const errorDiv = document.createElement('div');
                errorDiv.className = 'message error-message';
                errorDiv.textContent = `Error: ${message}`;
                errorDiv.style.color = '#ff6b6b';
                errorDiv.style.backgroundColor = '#ffe0e0';
                errorDiv.style.padding = '8px';
                errorDiv.style.borderRadius = '4px';
                messagesDiv.appendChild(errorDiv);
                // Re-enable send button on error
                sendButton.disabled = false;
                sendButton.textContent = 'Send';
                isStreaming = false;
                currentAssistantMessage = null; // Clear any partial message
                break;
                
            default:
                // Default handling for any other message types
                const defaultDiv = document.createElement('div');
                defaultDiv.className = `message ${sender}-message`;
                defaultDiv.textContent = message;
                messagesDiv.appendChild(defaultDiv);
                break;
        }
        
        // Auto-scroll to bottom
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }

    function sendMessage() {
        const message = messageInput.value.trim();
        if (message && !isStreaming) {
            addMessage(message, 'user');
            vscode.postMessage({
                command: 'sendMessage',
                text: message
            });
            messageInput.value = '';
            // Set streaming state immediately when sending a message
            isStreaming = true;
            sendButton.disabled = true;
            sendButton.textContent = 'Generating...';
        }
    }

    sendButton.addEventListener('click', sendMessage);
    
    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !isStreaming) {
            sendMessage();
        }
    });

    // Handle messages from the extension
    window.addEventListener('message', event => {
        const message = event.data;
        
        // Handle both old and new message formats
        if (message.command === 'addMessage') {
            addMessage(message.message, message.sender);
        } else if (message.command === 'systemMessage') {
            addMessage(message.message, 'system');
        } else if (message.command === 'pythonMessage') {
            // Handle messages from Python. Assume they are now plain text.
            addMessage(message.data, 'assistant'); 
        }
    });

    // Signal that webview is ready
    vscode.postMessage({ command: 'ready' });
})();
