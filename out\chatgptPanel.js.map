{"version": 3, "file": "chatgptPanel.js", "sourceRoot": "", "sources": ["../src/chatgptPanel.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,6BAA6B;AAC7B,iDAAoD;AAEpD,MAAa,YAAY;IAQd,MAAM,CAAC,YAAY,CAAC,YAAwB;QAC/C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB;YACzC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU;YAC3C,CAAC,CAAC,SAAS,CAAC;QAEhB,IAAI,YAAY,CAAC,YAAY,EAAE;YAC3B,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAChD,OAAO;SACV;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,SAAS,EACT,cAAc,EACd,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAC/B;YACI,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE;gBAChB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC;aAC7C;SACJ,CACJ,CAAC;QAEF,YAAY,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;IACtE,CAAC;IAED,YAAoB,KAA0B,EAAE,YAAwB;QA7BhE,iBAAY,GAAwB,EAAE,CAAC;QAEvC,mBAAc,GAAG,KAAK,CAAC;QA4B3B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAExE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CACnC,OAAO,CAAC,EAAE;YACN,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,YAAY;oBACb,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,OAAO;gBACX,KAAK,aAAa;oBACd,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACjC,OAAO;gBACX,KAAK,OAAO;oBACR,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;oBAC3B,OAAO;aACd;QACL,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,YAAY,CACpB,CAAC;IACN,CAAC;IAEM,WAAW,CAAC,OAAe;QAC9B,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC5B,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,MAAM;aACjB,CAAC,CAAC;YACH,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SAC/B;aAAM;YACH,wDAAwD;YACxD,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;SACrD;IACL,CAAC;IAEO,iBAAiB;QACrB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,mBAAmB,CAAC,CAAC;QAE7E,IAAI,CAAC,cAAc,GAAG,IAAA,qBAAK,EAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,EAAE;YAChD,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;SAClC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;YACtC,IAAI,MAAM,EAAE;gBACR,4DAA4D;gBAC5D,oFAAoF;gBACpF,IAAI,WAAW,GAAG,WAAW,CAAC,CAAC,UAAU;gBACzC,IAAI,cAAc,GAAG,MAAM,CAAC;gBAE5B,sDAAsD;gBACtD,IAAI,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;oBACpC,WAAW,GAAG,cAAc,CAAC;oBAC7B,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;iBAC7D;qBAAM,IAAI,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE;oBAC5C,WAAW,GAAG,eAAe,CAAC;oBAC9B,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;iBAC9D;qBAAM,IAAI,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;oBACvC,WAAW,GAAG,UAAU,CAAC;oBACzB,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;iBACzD;qBAAM,IAAI,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;oBACpC,WAAW,GAAG,OAAO,CAAC;oBACtB,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBACtD;gBAED,kCAAkC;gBAClC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC5B,OAAO,EAAE,YAAY;oBACrB,OAAO,EAAE,cAAc;oBACvB,MAAM,EAAE,WAAW;iBACtB,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;YACrC,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC5B,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,UAAU,KAAK,EAAE;gBAC1B,MAAM,EAAE,OAAO,CAAC,0BAA0B;aAC7C,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;YAC5B,OAAO,EAAE,eAAe;YACxB,OAAO,EAAE,oCAAoC;SAChD,CAAC,CAAC;IACP,CAAC;IAEO,aAAa,CAAC,OAAe;QACjC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;YAClD,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;SACnD;IACL,CAAC;IAEM,OAAO;QACV,YAAY,CAAC,YAAY,GAAG,SAAS,CAAC;QAEtC,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;SAC9B;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAEtB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YAC7B,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;YAClC,IAAI,CAAC,EAAE;gBACH,CAAC,CAAC,OAAO,EAAE,CAAC;aACf;SACJ;IACL,CAAC;IAEO,OAAO;QACX,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,cAAc,CAAC;QACnC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAChE,CAAC;IAEO,kBAAkB,CAAC,OAAuB;QAC9C,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAClC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC,CAC9D,CAAC;QAEF,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAqFgB,SAAS;;oBAEpB,CAAC;IACjB,CAAC;CACJ;AA3PD,oCA2PC"}