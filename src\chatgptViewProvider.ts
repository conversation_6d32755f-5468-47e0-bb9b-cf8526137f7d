import * as vscode from 'vscode';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';

export class ChatGPTViewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'chatgpt.chatView';
    private _view?: vscode.WebviewView;
    private _pythonProcess: ChildProcess | undefined;
    private _isInitialized = false;
    private _contextEnabled = true;

    constructor(private readonly _extensionUri: vscode.Uri) {
        // Initialize context setting from configuration
        this._contextEnabled = vscode.workspace.getConfiguration('chatgpt').get('includeContext', true);
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                vscode.Uri.joinPath(this._extensionUri, 'media')
            ]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        webviewView.webview.onDidReceiveMessage(
            message => {
                switch (message.command) {
                    case 'initialize':
                        this._initializePython();
                        return;
                    case 'sendMessage':
                        this._sendToPython(message.text);
                        return;
                    case 'ready':
                        this._isInitialized = true;
                        this._sendContextInfo();
                        return;
                    case 'requestContext':
                        this._sendContextInfo();
                        return;
                }
            }
        );
    }

    public sendMessage(message: string) {
        if (this._isInitialized && this._view) {
            this._view.webview.postMessage({
                command: 'addMessage',
                message: message,
                sender: 'user'
            });
            this._sendToPython(message);
        }
    }

    public refreshContext() {
        this._sendContextInfo();
    }

    public toggleContext() {
        this._contextEnabled = !this._contextEnabled;

        // Update the configuration
        vscode.workspace.getConfiguration('chatgpt').update('includeContext', this._contextEnabled, true);

        // Send status message
        if (this._view) {
            this._view.webview.postMessage({
                command: 'addMessage',
                message: `Context ${this._contextEnabled ? 'enabled' : 'disabled'}`,
                sender: 'system'
            });
        }

        // Update context display
        this._sendContextInfo();
    }

    private _sendContextInfo() {
        if (!this._view) return;

        const contextInfo = this._contextEnabled ? this._gatherContextInfo() : null;
        this._view.webview.postMessage({
            command: 'contextUpdate',
            context: contextInfo,
            contextEnabled: this._contextEnabled
        });
    }

    private _gatherContextInfo() {
        const config = vscode.workspace.getConfiguration('chatgpt');
        const includeFullContent = config.get('includeFullFileContent', false);
        const maxFileSize = config.get('maxFileSize', 10000);

        const context: any = {
            workspace: null,
            activeFile: null,
            openFiles: [],
            workspaceFiles: []
        };

        // Get workspace information
        if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
            context.workspace = {
                name: vscode.workspace.workspaceFolders[0].name,
                path: vscode.workspace.workspaceFolders[0].uri.fsPath
            };
        }

        // Get active editor information
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor) {
            const document = activeEditor.document;
            const fullContent = document.getText();

            // Determine what content to include
            let contentToInclude = '';
            let contentPreview = '';

            if (includeFullContent && fullContent.length <= maxFileSize) {
                contentToInclude = fullContent;
                contentPreview = `Full file content (${fullContent.length} chars)`;
            } else if (includeFullContent && fullContent.length > maxFileSize) {
                contentToInclude = fullContent.substring(0, maxFileSize) + '\n... [truncated]';
                contentPreview = `Partial file content (${maxFileSize}/${fullContent.length} chars)`;
            } else {
                // Just include a preview
                contentPreview = `File: ${document.lineCount} lines, ${fullContent.length} chars`;
            }

            context.activeFile = {
                fileName: path.basename(document.fileName),
                filePath: document.fileName,
                language: document.languageId,
                content: contentToInclude,
                contentPreview: contentPreview,
                lineCount: document.lineCount,
                characterCount: fullContent.length,
                selection: activeEditor.selection && !activeEditor.selection.isEmpty ? {
                    start: activeEditor.selection.start,
                    end: activeEditor.selection.end,
                    text: document.getText(activeEditor.selection)
                } : null
            };
        }

        // Get all open files
        context.openFiles = vscode.workspace.textDocuments
            .filter(doc => !doc.isUntitled && doc.uri.scheme === 'file')
            .map(doc => ({
                fileName: path.basename(doc.fileName),
                filePath: doc.fileName,
                language: doc.languageId,
                isDirty: doc.isDirty,
                lineCount: doc.lineCount
            }));

        return context;
    }

    private _initializePython() {
        // Prevent multiple Python processes
        if (this._pythonProcess) {
            return;
        }

        const pythonPath = path.join(__dirname, '..', 'python', 'chatgpt_bridge.py');

        this._pythonProcess = spawn('python', [pythonPath], {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        this._pythonProcess.stdout?.on('data', (data) => {
            const output = data.toString().trim();
            if (output && this._view) {
                // Try to parse the output to detect message type indicators
                let messageType = 'assistant'; // default
                let messageContent = output;
                
                // Check for special message type prefixes from Python
                if (output.startsWith('STREAM_START:')) {
                    messageType = 'stream_start';
                    messageContent = output.substring('STREAM_START:'.length);
                } else if (output.startsWith('STREAM_UPDATE:')) {
                    messageType = 'stream_update';
                    messageContent = output.substring('STREAM_UPDATE:'.length);
                } else if (output.startsWith('THINKING:')) {
                    messageType = 'thinking';
                    messageContent = output.substring('THINKING:'.length);
                } else if (output.startsWith('ERROR:')) {
                    messageType = 'error';
                    messageContent = output.substring('ERROR:'.length);
                }
                
                // Send the properly typed message
                this._view.webview.postMessage({
                    command: 'addMessage',
                    message: messageContent,
                    sender: messageType
                });
            }
        });

        this._pythonProcess.stderr?.on('data', (data) => {
            const error = data.toString().trim();
            console.error('Python error:', error);
            if (this._view) {
                this._view.webview.postMessage({
                    command: 'addMessage',
                    message: `Error: ${error}`,
                    sender: 'error'
                });
            }
        });

        if (this._view) {
            this._view.webview.postMessage({
                command: 'systemMessage',
                message: 'Initializing ChatGPT connection...'
            });
        }
    }

    private _sendToPython(message: string) {
        if (this._pythonProcess && this._pythonProcess.stdin) {
            let finalMessage = message;

            // Include context information with the message if enabled
            if (this._contextEnabled) {
                const contextInfo = this._gatherContextInfo();
                let contextPrefix = '';

                if (contextInfo.workspace) {
                    contextPrefix += `[Workspace: ${contextInfo.workspace.name}]\n`;
                }

                if (contextInfo.activeFile) {
                    contextPrefix += `[Active File: ${contextInfo.activeFile.fileName} (${contextInfo.activeFile.language})]\n`;

                    if (contextInfo.activeFile.content) {
                        contextPrefix += `[File Content:\n${contextInfo.activeFile.content}\n]\n`;
                    } else {
                        contextPrefix += `[File Info: ${contextInfo.activeFile.contentPreview}]\n`;
                    }

                    if (contextInfo.activeFile.selection && contextInfo.activeFile.selection.text.trim()) {
                        contextPrefix += `[Selected Code:\n${contextInfo.activeFile.selection.text}\n]\n`;
                    }
                }

                if (contextInfo.openFiles && contextInfo.openFiles.length > 0) {
                    const fileList = contextInfo.openFiles.map((f: any) => `${f.fileName} (${f.language})`).join(', ');
                    contextPrefix += `[Open Files: ${fileList}]\n`;
                }

                if (contextPrefix) {
                    finalMessage = `${contextPrefix}\n${message}`;
                }
            }

            this._pythonProcess.stdin.write(finalMessage + '\n');
        }
    }

    public dispose() {
        if (this._pythonProcess) {
            this._pythonProcess.kill();
            this._pythonProcess = undefined;
        }
        this._isInitialized = false;
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        const scriptUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'media', 'sidebar.js')
        );

        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>ChatGPT</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        padding: 10px;
                        color: var(--vscode-foreground);
                        background-color: var(--vscode-editor-background);
                        margin: 0;
                    }
                    .chat-container {
                        display: flex;
                        flex-direction: column;
                        height: 100vh;
                    }
                    .context-info {
                        background-color: var(--vscode-textBlockQuote-background);
                        border-left: 4px solid var(--vscode-textBlockQuote-border);
                        padding: 8px;
                        margin-bottom: 10px;
                        font-size: 0.85em;
                        border-radius: 4px;
                    }
                    .context-info h4 {
                        margin: 0 0 5px 0;
                        color: var(--vscode-textLink-foreground);
                    }
                    .context-info p {
                        margin: 2px 0;
                        opacity: 0.8;
                    }
                    .messages {
                        flex: 1;
                        overflow-y: auto;
                        border: 1px solid var(--vscode-panel-border);
                        padding: 8px;
                        margin-bottom: 10px;
                        background-color: var(--vscode-editor-background);
                        border-radius: 4px;
                    }
                    .message {
                        margin-bottom: 8px;
                        padding: 6px;
                        border-radius: 4px;
                        font-size: 0.9em;
                    }
                    .user-message {
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        text-align: right;
                    }
                    .assistant-message {
                        background-color: var(--vscode-textBlockQuote-background);
                        border-left: 4px solid var(--vscode-textBlockQuote-border);
                    }
                    .system-message {
                        background-color: var(--vscode-inputValidation-infoBackground);
                        color: var(--vscode-inputValidation-infoForeground);
                        font-style: italic;
                    }
                    .thinking-message {
                        background-color: var(--vscode-inputValidation-warningBackground);
                        color: var(--vscode-inputValidation-warningForeground);
                        font-style: italic;
                        opacity: 0.8;
                        font-size: 0.8em;
                    }
                    .input-container {
                        display: flex;
                        gap: 8px;
                    }
                    #messageInput {
                        flex: 1;
                        padding: 6px;
                        background-color: var(--vscode-input-background);
                        color: var(--vscode-input-foreground);
                        border: 1px solid var(--vscode-input-border);
                        border-radius: 4px;
                        font-size: 0.9em;
                    }
                    #sendButton {
                        padding: 6px 12px;
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 0.9em;
                    }
                    #sendButton:hover {
                        background-color: var(--vscode-button-hoverBackground);
                    }
                    #sendButton:disabled {
                        opacity: 0.6;
                        cursor: not-allowed;
                    }
                </style>
            </head>
            <body>
                <div class="chat-container">
                    <div id="contextInfo" class="context-info" style="display: none;">
                        <h4>Context</h4>
                        <div id="contextContent"></div>
                    </div>
                    <div id="messages" class="messages"></div>
                    <div class="input-container">
                        <input type="text" id="messageInput" placeholder="Ask ChatGPT..." />
                        <button id="sendButton">Send</button>
                    </div>
                </div>
                <script src="${scriptUri}"></script>
            </body>
            </html>`;
    }
}
