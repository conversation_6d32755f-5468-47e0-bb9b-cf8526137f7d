{"version": 3, "file": "chatgptViewProvider.js", "sourceRoot": "", "sources": ["../src/chatgptViewProvider.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,6BAA6B;AAC7B,iDAAoD;AAEpD,MAAa,mBAAmB;IAO5B,YAA6B,aAAyB;QAAzB,kBAAa,GAAb,aAAa,CAAY;QAH9C,mBAAc,GAAG,KAAK,CAAC;QACvB,oBAAe,GAAG,IAAI,CAAC;QAG3B,gDAAgD;QAChD,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IACpG,CAAC;IAEM,kBAAkB,CACrB,WAA+B,EAC/B,OAAyC,EACzC,MAAgC;QAEhC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QAEzB,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE;gBAChB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC;aACnD;SACJ,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAExE,WAAW,CAAC,OAAO,CAAC,mBAAmB,CACnC,OAAO,CAAC,EAAE;YACN,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,YAAY;oBACb,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,OAAO;gBACX,KAAK,aAAa;oBACd,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACjC,OAAO;gBACX,KAAK,OAAO;oBACR,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;oBAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO;gBACX,KAAK,gBAAgB;oBACjB,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO;aACd;QACL,CAAC,CACJ,CAAC;IACN,CAAC;IAEM,WAAW,CAAC,OAAe;QAC9B,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,KAAK,EAAE;YACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,MAAM;aACjB,CAAC,CAAC;YACH,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SAC/B;IACL,CAAC;IAEM,cAAc;QACjB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAEM,aAAa;QAChB,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC;QAE7C,2BAA2B;QAC3B,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QAElG,sBAAsB;QACtB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,WAAW,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE;gBACnE,MAAM,EAAE,QAAQ;aACnB,CAAC,CAAC;SACN;QAED,yBAAyB;QACzB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAEO,gBAAgB;QACpB,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE,OAAO;QAExB,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5E,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;YAC3B,OAAO,EAAE,eAAe;YACxB,OAAO,EAAE,WAAW;YACpB,cAAc,EAAE,IAAI,CAAC,eAAe;SACvC,CAAC,CAAC;IACP,CAAC;IAEO,kBAAkB;QACtB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC5D,MAAM,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QACvE,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAErD,MAAM,OAAO,GAAQ;YACjB,SAAS,EAAE,IAAI;YACf,UAAU,EAAE,IAAI;YAChB,SAAS,EAAE,EAAE;YACb,cAAc,EAAE,EAAE;SACrB,CAAC;QAEF,4BAA4B;QAC5B,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;YACnF,OAAO,CAAC,SAAS,GAAG;gBAChB,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI;gBAC/C,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM;aACxD,CAAC;SACL;QAED,gCAAgC;QAChC,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACpD,IAAI,YAAY,EAAE;YACd,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;YACvC,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAEvC,oCAAoC;YACpC,IAAI,gBAAgB,GAAG,EAAE,CAAC;YAC1B,IAAI,cAAc,GAAG,EAAE,CAAC;YAExB,IAAI,kBAAkB,IAAI,WAAW,CAAC,MAAM,IAAI,WAAW,EAAE;gBACzD,gBAAgB,GAAG,WAAW,CAAC;gBAC/B,cAAc,GAAG,sBAAsB,WAAW,CAAC,MAAM,SAAS,CAAC;aACtE;iBAAM,IAAI,kBAAkB,IAAI,WAAW,CAAC,MAAM,GAAG,WAAW,EAAE;gBAC/D,gBAAgB,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,GAAG,mBAAmB,CAAC;gBAC/E,cAAc,GAAG,yBAAyB,WAAW,IAAI,WAAW,CAAC,MAAM,SAAS,CAAC;aACxF;iBAAM;gBACH,yBAAyB;gBACzB,cAAc,GAAG,SAAS,QAAQ,CAAC,SAAS,WAAW,WAAW,CAAC,MAAM,QAAQ,CAAC;aACrF;YAED,OAAO,CAAC,UAAU,GAAG;gBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC1C,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,QAAQ,EAAE,QAAQ,CAAC,UAAU;gBAC7B,OAAO,EAAE,gBAAgB;gBACzB,cAAc,EAAE,cAAc;gBAC9B,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,cAAc,EAAE,WAAW,CAAC,MAAM;gBAClC,SAAS,EAAE,YAAY,CAAC,SAAS,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;oBACnE,KAAK,EAAE,YAAY,CAAC,SAAS,CAAC,KAAK;oBACnC,GAAG,EAAE,YAAY,CAAC,SAAS,CAAC,GAAG;oBAC/B,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC;iBACjD,CAAC,CAAC,CAAC,IAAI;aACX,CAAC;SACL;QAED,qBAAqB;QACrB,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa;aAC7C,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC;aAC3D,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACT,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC;YACrC,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,QAAQ,EAAE,GAAG,CAAC,UAAU;YACxB,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,SAAS,EAAE,GAAG,CAAC,SAAS;SAC3B,CAAC,CAAC,CAAC;QAER,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,iBAAiB;QACrB,oCAAoC;QACpC,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,OAAO;SACV;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,mBAAmB,CAAC,CAAC;QAE7E,IAAI,CAAC,cAAc,GAAG,IAAA,qBAAK,EAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,EAAE;YAChD,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;SAClC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;YACtC,IAAI,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE;gBACtB,4DAA4D;gBAC5D,IAAI,WAAW,GAAG,WAAW,CAAC,CAAC,UAAU;gBACzC,IAAI,cAAc,GAAG,MAAM,CAAC;gBAE5B,sDAAsD;gBACtD,IAAI,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;oBACpC,WAAW,GAAG,cAAc,CAAC;oBAC7B,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;iBAC7D;qBAAM,IAAI,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE;oBAC5C,WAAW,GAAG,eAAe,CAAC;oBAC9B,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;iBAC9D;qBAAM,IAAI,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;oBACvC,WAAW,GAAG,UAAU,CAAC;oBACzB,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;iBACzD;qBAAM,IAAI,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;oBACpC,WAAW,GAAG,OAAO,CAAC;oBACtB,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBACtD;gBAED,kCAAkC;gBAClC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC3B,OAAO,EAAE,YAAY;oBACrB,OAAO,EAAE,cAAc;oBACvB,MAAM,EAAE,WAAW;iBACtB,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;YACrC,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACtC,IAAI,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC3B,OAAO,EAAE,YAAY;oBACrB,OAAO,EAAE,UAAU,KAAK,EAAE;oBAC1B,MAAM,EAAE,OAAO;iBAClB,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,OAAO,EAAE,eAAe;gBACxB,OAAO,EAAE,oCAAoC;aAChD,CAAC,CAAC;SACN;IACL,CAAC;IAEO,aAAa,CAAC,OAAe;QACjC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;YAClD,IAAI,YAAY,GAAG,OAAO,CAAC;YAE3B,0DAA0D;YAC1D,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC9C,IAAI,aAAa,GAAG,EAAE,CAAC;gBAEvB,IAAI,WAAW,CAAC,SAAS,EAAE;oBACvB,aAAa,IAAI,eAAe,WAAW,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC;iBACnE;gBAED,IAAI,WAAW,CAAC,UAAU,EAAE;oBACxB,aAAa,IAAI,iBAAiB,WAAW,CAAC,UAAU,CAAC,QAAQ,KAAK,WAAW,CAAC,UAAU,CAAC,QAAQ,MAAM,CAAC;oBAE5G,IAAI,WAAW,CAAC,UAAU,CAAC,OAAO,EAAE;wBAChC,aAAa,IAAI,mBAAmB,WAAW,CAAC,UAAU,CAAC,OAAO,OAAO,CAAC;qBAC7E;yBAAM;wBACH,aAAa,IAAI,eAAe,WAAW,CAAC,UAAU,CAAC,cAAc,KAAK,CAAC;qBAC9E;oBAED,IAAI,WAAW,CAAC,UAAU,CAAC,SAAS,IAAI,WAAW,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;wBAClF,aAAa,IAAI,oBAAoB,WAAW,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC;qBACrF;iBACJ;gBAED,IAAI,WAAW,CAAC,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC3D,MAAM,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACnG,aAAa,IAAI,gBAAgB,QAAQ,KAAK,CAAC;iBAClD;gBAED,IAAI,aAAa,EAAE;oBACf,YAAY,GAAG,GAAG,aAAa,KAAK,OAAO,EAAE,CAAC;iBACjD;aACJ;YAED,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC;SACxD;IACL,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;YAC3B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;SACnC;QACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAChC,CAAC;IAEO,kBAAkB,CAAC,OAAuB;QAC9C,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAClC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,CAAC,CACjE,CAAC;QAEF,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAkHgB,SAAS;;oBAEpB,CAAC;IACjB,CAAC;;AA/YL,kDAgZC;AA/Y0B,4BAAQ,GAAG,kBAAkB,CAAC"}