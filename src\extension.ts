import * as vscode from 'vscode';
import { ChatGPTPanel } from './chatgptPanel';

export function activate(context: vscode.ExtensionContext) {
    console.log('ChatGPT extension is now active!');

    // Register command to open chat panel
    let openChatDisposable = vscode.commands.registerCommand('chatgpt.openChat', () => {
        ChatGPTPanel.createOrShow(context.extensionUri);
    });

    // Register command to ask a quick question
    let askQuestionDisposable = vscode.commands.registerCommand('chatgpt.askQuestion', async () => {
        const question = await vscode.window.showInputBox({
            prompt: 'What would you like to ask ChatGPT?',
            placeHolder: 'Enter your question here...'
        });

        if (question) {
            ChatGPTPanel.createOrShow(context.extensionUri);
            ChatGPTPanel.currentPanel?.sendMessage(question);
        }
    });

    context.subscriptions.push(openChatDisposable, askQuestionDisposable);
}

export function deactivate() {}