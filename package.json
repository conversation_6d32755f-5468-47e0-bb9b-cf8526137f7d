{"name": "chatgpt-integration", "displayName": "ChatGPT Integration", "description": "Talk to ChatGPT directly from VS Code", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "chatgpt-sidebar", "title": "ChatGPT", "icon": "$(comment-discussion)"}]}, "views": {"chatgpt-sidebar": [{"type": "webview", "id": "chatgpt.chatView", "name": "Cha<PERSON>", "icon": "$(comment)", "when": "true"}]}, "commands": [{"command": "chatgpt.openChat", "title": "Open ChatGPT Chat", "category": "ChatGPT"}, {"command": "chatgpt.askQuestion", "title": "Ask ChatGPT", "category": "ChatGPT"}, {"command": "chatgpt.refreshContext", "title": "Refresh Context", "category": "ChatGPT", "icon": "$(refresh)"}, {"command": "chatgpt.toggleContext", "title": "Toggle Context", "category": "ChatGPT", "icon": "$(eye)"}], "menus": {"view/title": [{"command": "chatgpt.refreshContext", "when": "view == chatgpt.chatView", "group": "navigation"}, {"command": "chatgpt.toggleContext", "when": "view == chatgpt.chatView", "group": "navigation"}]}, "keybindings": [{"command": "chatgpt.askQuestion", "key": "ctrl+shift+g", "mac": "cmd+shift+g"}], "configuration": {"title": "ChatGPT", "properties": {"chatgpt.includeContext": {"type": "boolean", "default": true, "description": "Include file and workspace context in ChatGPT messages"}, "chatgpt.includeFullFileContent": {"type": "boolean", "default": false, "description": "Include the full content of the active file (may use more tokens)"}, "chatgpt.maxFileSize": {"type": "number", "default": 10000, "description": "Maximum file size in characters to include as context"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}}