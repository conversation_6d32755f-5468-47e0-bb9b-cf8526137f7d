{"name": "chatgpt-integration", "displayName": "ChatGPT Integration", "description": "Talk to ChatGPT directly from VS Code", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "chatgpt.openChat", "title": "Open ChatGPT Chat", "category": "ChatGPT"}, {"command": "chatgpt.askQuestion", "title": "Ask ChatGPT", "category": "ChatGPT"}], "keybindings": [{"command": "chatgpt.askQuestion", "key": "ctrl+shift+g", "mac": "cmd+shift+g"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}}