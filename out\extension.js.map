{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,+DAA4D;AAE5D,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAEhD,mCAAmC;IACnC,MAAM,YAAY,GAAG,IAAI,yCAAmB,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAC5E,kBAAkB,GAAG,YAAY,CAAC;IAElC,qCAAqC;IACrC,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,yCAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC,CACxF,CAAC;IAEF,mEAAmE;IACnE,IAAI,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAC9E,wCAAwC;QACxC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,2CAA2C;IAC3C,IAAI,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QAC1F,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC9C,MAAM,EAAE,qCAAqC;YAC7C,WAAW,EAAE,6BAA6B;SAC7C,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE;YACV,8CAA8C;YAC9C,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;YAC/D,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;SACtC;IACL,CAAC,CAAC,CAAC;IAEH,sCAAsC;IACtC,IAAI,wBAAwB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,GAAG,EAAE;QAC1F,YAAY,CAAC,cAAc,EAAE,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,qCAAqC;IACrC,IAAI,uBAAuB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACxF,YAAY,CAAC,aAAa,EAAE,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,qDAAqD;IACrD,IAAI,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,GAAG,EAAE;QACxE,YAAY,CAAC,cAAc,EAAE,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,qDAAqD;IACrD,IAAI,sBAAsB,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,GAAG,EAAE;QACvE,yDAAyD;QACzD,UAAU,CAAC,GAAG,EAAE;YACZ,YAAY,CAAC,cAAc,EAAE,CAAC;QAClC,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,kBAAkB,EAClB,qBAAqB,EACrB,wBAAwB,EACxB,uBAAuB,EACvB,sBAAsB,EACtB,sBAAsB,CACzB,CAAC;AACN,CAAC;AA/DD,4BA+DC;AAED,IAAI,kBAAmD,CAAC;AAExD,SAAgB,UAAU;IACtB,IAAI,kBAAkB,EAAE;QACpB,kBAAkB,CAAC,OAAO,EAAE,CAAC;KAChC;AACL,CAAC;AAJD,gCAIC"}