"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const chatgptViewProvider_1 = require("./chatgptViewProvider");
function activate(context) {
    console.log('ChatGPT extension is now active!');
    // Create the webview view provider
    const chatProvider = new chatgptViewProvider_1.ChatGPTViewProvider(context.extensionUri, context);
    globalChatProvider = chatProvider;
    // Register the webview view provider
    context.subscriptions.push(vscode.window.registerWebviewViewProvider(chatgptViewProvider_1.ChatGPTViewProvider.viewType, chatProvider));
    // Register command to open chat panel (fallback for compatibility)
    let openChatDisposable = vscode.commands.registerCommand('chatgpt.openChat', () => {
        // Focus the ChatGPT view in the sidebar
        vscode.commands.executeCommand('chatgpt.chatView.focus');
    });
    // Register command to ask a quick question
    let askQuestionDisposable = vscode.commands.registerCommand('chatgpt.askQuestion', async () => {
        const question = await vscode.window.showInputBox({
            prompt: 'What would you like to ask ChatGPT?',
            placeHolder: 'Enter your question here...'
        });
        if (question) {
            // Focus the ChatGPT view and send the message
            await vscode.commands.executeCommand('chatgpt.chatView.focus');
            chatProvider.sendMessage(question);
        }
    });
    // Register command to refresh context
    let refreshContextDisposable = vscode.commands.registerCommand('chatgpt.refreshContext', () => {
        chatProvider.refreshContext();
    });
    // Register command to toggle context
    let toggleContextDisposable = vscode.commands.registerCommand('chatgpt.toggleContext', () => {
        chatProvider.toggleContext();
    });
    // Listen for active editor changes to update context
    let activeEditorDisposable = vscode.window.onDidChangeActiveTextEditor(() => {
        chatProvider.refreshContext();
    });
    // Listen for text document changes to update context
    let textDocumentDisposable = vscode.workspace.onDidChangeTextDocument(() => {
        // Debounce context updates to avoid too frequent updates
        setTimeout(() => {
            chatProvider.refreshContext();
        }, 1000);
    });
    context.subscriptions.push(openChatDisposable, askQuestionDisposable, refreshContextDisposable, toggleContextDisposable, activeEditorDisposable, textDocumentDisposable);
}
exports.activate = activate;
let globalChatProvider;
function deactivate() {
    if (globalChatProvider) {
        globalChatProvider.dispose();
    }
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map