"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const chatgptPanel_1 = require("./chatgptPanel");
function activate(context) {
    console.log('ChatGPT extension is now active!');
    // Register command to open chat panel
    let openChatDisposable = vscode.commands.registerCommand('chatgpt.openChat', () => {
        chatgptPanel_1.ChatGPTPanel.createOrShow(context.extensionUri);
    });
    // Register command to ask a quick question
    let askQuestionDisposable = vscode.commands.registerCommand('chatgpt.askQuestion', async () => {
        const question = await vscode.window.showInputBox({
            prompt: 'What would you like to ask ChatGPT?',
            placeHolder: 'Enter your question here...'
        });
        if (question) {
            chatgptPanel_1.ChatGPTPanel.createOrShow(context.extensionUri);
            chatgptPanel_1.ChatGPTPanel.currentPanel?.sendMessage(question);
        }
    });
    context.subscriptions.push(openChatDisposable, askQuestionDisposable);
}
exports.activate = activate;
function deactivate() { }
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map