(function() {
    const vscode = acquireVsCodeApi();
    
    const messagesDiv = document.getElementById('messages');
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    const contextInfo = document.getElementById('contextInfo');
    const contextContent = document.getElementById('contextContent');
    
    let currentAssistantMessage = null;
    let isStreaming = false;
    let currentContext = null;

    // Initialize Python bridge
    vscode.postMessage({ command: 'initialize' });

    function addMessage(message, sender) {
        // Handle different message types
        switch(sender) {
            case 'stream_start':
                // Start a new streaming message
                currentAssistantMessage = document.createElement('div');
                currentAssistantMessage.className = 'message assistant-message';
                currentAssistantMessage.textContent = '';
                messagesDiv.appendChild(currentAssistantMessage);
                isStreaming = true;
                sendButton.disabled = true;
                sendButton.textContent = 'Generating...';
                break;
                
            case 'stream_update':
                // Update the existing streaming message
                if (currentAssistantMessage) {
                    currentAssistantMessage.textContent = message;
                }
                break;
                
            case 'assistant':
                // This is the final complete message
                if (currentAssistantMessage) {
                    currentAssistantMessage.textContent = message;
                } else {
                    // Fallback for non-streaming assistant messages
                    const assistantDiv = document.createElement('div');
                    assistantDiv.className = 'message assistant-message';
                    assistantDiv.textContent = message;
                    messagesDiv.appendChild(assistantDiv);
                }
                currentAssistantMessage = null; // Reset for next message
                isStreaming = false;
                sendButton.disabled = false;
                sendButton.textContent = 'Send';
                break;
                
            case 'thinking':
                // Show thinking indicators temporarily
                const thinkingDiv = document.createElement('div');
                thinkingDiv.className = 'message thinking-message';
                thinkingDiv.textContent = message;
                messagesDiv.appendChild(thinkingDiv);
                
                // Remove thinking indicator after a few seconds
                setTimeout(() => {
                    if (thinkingDiv.parentNode) {
                        thinkingDiv.parentNode.removeChild(thinkingDiv);
                    }
                }, 5000);
                break;
                
            case 'user':
                // User messages
                const userDiv = document.createElement('div');
                userDiv.className = 'message user-message';
                userDiv.textContent = message;
                messagesDiv.appendChild(userDiv);
                break;
                
            case 'system':
                // System messages
                const systemDiv = document.createElement('div');
                systemDiv.className = 'message system-message';
                systemDiv.textContent = message;
                messagesDiv.appendChild(systemDiv);
                break;
                
            case 'error':
                // Error messages
                const errorDiv = document.createElement('div');
                errorDiv.className = 'message error-message';
                errorDiv.textContent = `Error: ${message}`;
                errorDiv.style.color = '#ff6b6b';
                errorDiv.style.backgroundColor = '#ffe0e0';
                errorDiv.style.padding = '8px';
                errorDiv.style.borderRadius = '4px';
                messagesDiv.appendChild(errorDiv);
                // Re-enable send button on error
                sendButton.disabled = false;
                sendButton.textContent = 'Send';
                isStreaming = false;
                currentAssistantMessage = null; // Clear any partial message
                break;
                
            default:
                // Default handling for any other message types
                const defaultDiv = document.createElement('div');
                defaultDiv.className = `message ${sender}-message`;
                defaultDiv.textContent = message;
                messagesDiv.appendChild(defaultDiv);
                break;
        }
        
        // Auto-scroll to bottom
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }

    function updateContextDisplay(context, contextEnabled = true) {
        currentContext = context;

        if (!contextEnabled) {
            contextContent.innerHTML = '<p><em>Context disabled</em></p>';
            contextInfo.style.display = 'block';
            return;
        }

        if (!context || (!context.activeFile && !context.workspace)) {
            contextInfo.style.display = 'none';
            return;
        }

        let contextHtml = '';

        if (context.workspace) {
            contextHtml += `<p><strong>Workspace:</strong> ${context.workspace.name}</p>`;
        }

        if (context.activeFile) {
            contextHtml += `<p><strong>Active File:</strong> ${context.activeFile.fileName} (${context.activeFile.language})</p>`;

            if (context.activeFile.contentPreview) {
                contextHtml += `<p><strong>Content:</strong> ${context.activeFile.contentPreview}</p>`;
            }

            if (context.activeFile.selection && context.activeFile.selection.text.trim()) {
                const selectionText = context.activeFile.selection.text.trim();
                const preview = selectionText.length > 100 ?
                    selectionText.substring(0, 100) + '...' : selectionText;
                contextHtml += `<p><strong>Selection:</strong> ${preview}</p>`;
            }
        }

        if (context.openFiles && context.openFiles.length > 0) {
            const fileCount = context.openFiles.length;
            const fileNames = context.openFiles.slice(0, 3).map(f => f.fileName).join(', ');
            const moreText = fileCount > 3 ? ` and ${fileCount - 3} more` : '';
            contextHtml += `<p><strong>Open Files:</strong> ${fileNames}${moreText}</p>`;
        }

        contextContent.innerHTML = contextHtml;
        contextInfo.style.display = 'block';
    }

    function sendMessage() {
        const message = messageInput.value.trim();
        if (message && !isStreaming) {
            addMessage(message, 'user'); // Show original message to user

            // Send the message - context will be added by the backend if enabled
            vscode.postMessage({
                command: 'sendMessage',
                text: message
            });

            messageInput.value = '';
            // Set streaming state immediately when sending a message
            isStreaming = true;
            sendButton.disabled = true;
            sendButton.textContent = 'Generating...';
        }
    }

    sendButton.addEventListener('click', sendMessage);
    
    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !isStreaming) {
            sendMessage();
        }
    });

    // Handle messages from the extension
    window.addEventListener('message', event => {
        const message = event.data;
        
        // Handle message formats
        if (message.command === 'addMessage') {
            addMessage(message.message, message.sender);
        } else if (message.command === 'systemMessage') {
            addMessage(message.message, 'system');
        } else if (message.command === 'contextUpdate') {
            updateContextDisplay(message.context, message.contextEnabled);
        }
    });

    // Request initial context when ready
    vscode.postMessage({ command: 'ready' });
})();
