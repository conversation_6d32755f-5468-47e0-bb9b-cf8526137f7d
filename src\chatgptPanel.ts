import * as vscode from 'vscode';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';

export class ChatGPTPanel {
    public static currentPanel: ChatGPTPanel | undefined;
    private readonly _panel: vscode.WebviewPanel;
    private readonly _extensionUri: vscode.Uri;
    private _disposables: vscode.Disposable[] = [];
    private _pythonProcess: ChildProcess | undefined;
    private _isInitialized = false;

    public static createOrShow(extensionUri: vscode.Uri) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        if (ChatGPTPanel.currentPanel) {
            ChatGPTPanel.currentPanel._panel.reveal(column);
            return;
        }

        const panel = vscode.window.createWebviewPanel(
            'chatgpt',
            'ChatGPT Chat',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(extensionUri, 'media')
                ]
            }
        );

        ChatGPTPanel.currentPanel = new ChatGPTPanel(panel, extensionUri);
    }

    private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
        this._panel = panel;
        this._extensionUri = extensionUri;

        this._update();
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);
        
        this._panel.webview.onDidReceiveMessage(
            message => {
                switch (message.command) {
                    case 'initialize':
                        this._initializePython();
                        return;
                    case 'sendMessage':
                        this._sendToPython(message.text);
                        return;
                    case 'ready':
                        this._isInitialized = true;
                        return;
                }
            },
            null,
            this._disposables
        );
    }

    public sendMessage(message: string) {
        if (this._isInitialized) {
            this._panel.webview.postMessage({
                command: 'addMessage',
                message: message,
                sender: 'user'
            });
            this._sendToPython(message);
        } else {
            // Queue the message for when initialization is complete
            setTimeout(() => this.sendMessage(message), 1000);
        }
    }

    private _initializePython() {
        const pythonPath = path.join(__dirname, '..', 'python', 'chatgpt_bridge.py');
        
        this._pythonProcess = spawn('python', [pythonPath], {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        this._pythonProcess.stdout?.on('data', (data) => {
            const output = data.toString().trim();
            if (output) {
                // Directly send the output as a plain text message
                this._panel.webview.postMessage({
                    command: 'pythonMessage', // Use pythonMessage command
                    data: output // Send raw string
                });
            }
        });

        this._pythonProcess.stderr?.on('data', (data) => {
            const error = data.toString().trim();
            console.error('Python error:', error);
            this._panel.webview.postMessage({
                command: 'addMessage', // Revert to addMessage for errors
                message: `Error: ${error}`,
                sender: 'error' // Use 'error' sender type
            });
        });

        this._panel.webview.postMessage({
            command: 'systemMessage',
            message: 'Initializing ChatGPT connection...'
        });
    }

    private _sendToPython(message: string) {
        if (this._pythonProcess && this._pythonProcess.stdin) {
            this._pythonProcess.stdin.write(message + '\n');
        }
    }

    public dispose() {
        ChatGPTPanel.currentPanel = undefined;
        
        if (this._pythonProcess) {
            this._pythonProcess.kill();
        }

        this._panel.dispose();

        while (this._disposables.length) {
            const x = this._disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }

    private _update() {
        const webview = this._panel.webview;
        this._panel.title = 'ChatGPT Chat';
        this._panel.webview.html = this._getHtmlForWebview(webview);
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        const scriptUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'media', 'main.js')
        );

        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>ChatGPT Chat</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        padding: 20px;
                        color: var(--vscode-foreground);
                        background-color: var(--vscode-editor-background);
                    }
                    .chat-container {
                        display: flex;
                        flex-direction: column;
                        height: 100vh;
                    }
                    .messages {
                        flex: 1;
                        overflow-y: auto;
                        border: 1px solid var(--vscode-panel-border);
                        padding: 10px;
                        margin-bottom: 10px;
                        background-color: var(--vscode-editor-background);
                    }
                    .message {
                        margin-bottom: 10px;
                        padding: 8px;
                        border-radius: 4px;
                    }
                    .user-message {
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        text-align: right;
                    }
                    .assistant-message {
                        background-color: var(--vscode-textBlockQuote-background);
                        border-left: 4px solid var(--vscode-textBlockQuote-border);
                    }
                    .system-message {
                        background-color: var(--vscode-inputValidation-infoBackground);
                        color: var(--vscode-inputValidation-infoForeground);
                        font-style: italic;
                    }
                    .input-container {
                        display: flex;
                        gap: 10px;
                    }
                    #messageInput {
                        flex: 1;
                        padding: 8px;
                        background-color: var(--vscode-input-background);
                        color: var(--vscode-input-foreground);
                        border: 1px solid var(--vscode-input-border);
                        border-radius: 4px;
                    }
                    #sendButton {
                        padding: 8px 16px;
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                    }
                    #sendButton:hover {
                        background-color: var(--vscode-button-hoverBackground);
                    }
                </style>
            </head>
            <body>
                <div class="chat-container">
                    <div id="messages" class="messages"></div>
                    <div class="input-container">
                        <input type="text" id="messageInput" placeholder="Type your message here..." />
                        <button id="sendButton">Send</button>
                    </div>
                </div>
                <script src="${scriptUri}"></script>
            </body>
            </html>`;
    }
}
