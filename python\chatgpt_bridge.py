import sys
import json
import os
import threading
import queue
import time
import io

# Set stdout to handle UTF-8 encoding properly
if sys.platform == "win32":
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# Import your existing script components
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import NoSuchElementException, TimeoutException

class ChatGPTBridge:
    def __init__(self):
        self.message_queue = queue.Queue()
        self.running = True
        self.driver = None
        self.search_enabled = False
        self.last_response_text = ""
        self.initialized = False
        self.current_streaming = False
        
    def send_to_vscode(self, message, message_type="assistant"):
        """Send message back to VS Code with proper encoding"""
        try:
            clean_message = self.clean_text_for_output(message)
            # Add message type prefix for proper handling in TypeScript
            if message_type == "stream_start":
                prefixed_message = f"STREAM_START:{clean_message}"
            elif message_type == "stream_update":
                prefixed_message = f"STREAM_UPDATE:{clean_message}"
            elif message_type == "thinking":
                prefixed_message = f"THINKING:{clean_message}"
            elif message_type == "error":
                prefixed_message = f"ERROR:{clean_message}"
            else:
                # Default to assistant message (no prefix needed)
                prefixed_message = clean_message

            print(prefixed_message, flush=True)
        except Exception as e:
            error_response = f"ERROR:Encoding error: {str(e)}"
            print(error_response, flush=True)
    
    def clean_text_for_output(self, text):
        """Clean text to avoid encoding issues"""
        if not text:
            return ""
        
        replacements = {
            '\u2018': "'", '\u2019': "'", '\u201c': '"', '\u201d': '"',
            '\u2013': '-', '\u2014': '--', '\u2026': '...',
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        try:
            text.encode('utf-8')
            return text
        except UnicodeEncodeError:
            return ''.join(char if ord(char) < 65536 else '?' for char in text)
    
    def initialize_driver(self):
        """Initialize the Chrome driver"""
        try:
            data_dir = os.path.join(os.getcwd(), "chromedriverdata")
            os.makedirs(data_dir, exist_ok=True)

            options = uc.ChromeOptions()
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--enable-unsafe-swiftshader")
            options.add_argument(f"--user-data-dir={data_dir}")
            options.add_argument("--profile-directory=Default")
            options.add_argument("--disable-web-security")
            options.add_argument("--disable-features=VizDisplayCompositor")

            self.driver = uc.Chrome(version_main=132, options=options)
            self.driver.get("https://chatgpt.com/?temporary-chat=true")
            
            self.send_to_vscode("Chrome driver initialized. Please log in manually if needed.", "system")
            time.sleep(5)
            
            self.last_response_text = self.get_latest_response_text()
            self.initialized = True
            self.send_to_vscode("ChatGPT connection ready!", "system")
            
        except Exception as e:
            self.send_to_vscode(f"Failed to initialize: {str(e)}", "error")
    
    def get_latest_response_text(self):
        """Get the latest response from ChatGPT"""
        if not self.driver:
            return ""
        
        try:
            self.handle_popups()
            responses = self.driver.find_elements(By.CSS_SELECTOR, "div.markdown.prose")
            if responses:
                return responses[-1].text.strip()
        except Exception:
            pass
        return ""
    
    def handle_popups(self):
        """Handle popups"""
        if not self.driver:
            return False
            
        try:
            stay_logged_out = self.driver.find_element(By.CSS_SELECTOR, 'a[href="#"].text-token-text-secondary.mt-5.cursor-pointer.text-sm.font-semibold.underline')
            if stay_logged_out.is_displayed():
                stay_logged_out.click()
                time.sleep(1)
                return True
        except NoSuchElementException:
            pass
        
        try:
            stay_logged_out_alt = self.driver.find_element(By.XPATH, '//a[contains(text(), "Stay logged out")]')
            if stay_logged_out_alt.is_displayed():
                stay_logged_out_alt.click()
                time.sleep(1)
                return True
        except NoSuchElementException:
            pass
            
        return False
    
    def should_use_search(self, message):
        """Determine if search is needed"""
        search_keywords = [
            'search for', 'look up', 'find information', 'google', 'research',
            'latest', 'recent', 'current', 'today', 'this week', 'this month', 
            'news', 'breaking', 'update', 'what happened',
            'weather', 'stock price', 'exchange rate', 'score', 'results',
            'when did', 'who is currently', 'what is the current', 'how many people',
            'can you search', 'please search', 'look this up', 'check online',
            '2024', '2025', 'recently announced', 'just released'
        ]
        
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in search_keywords)
    
    def check_thinking_indicators(self):
        """Check for thinking/processing indicators"""
        if not self.driver:
            return []
            
        thinking_messages = []
        try:
            thinking_spans = self.driver.find_elements(By.CSS_SELECTOR, 'span.flex.items-center.gap-1.text-start.align-middle.text-token-text-secondary')
            for span in thinking_spans:
                if span.is_displayed() and span.text.strip():
                    text = span.text.strip()
                    if any(keyword in text.lower() for keyword in ['thought', 'thinking', 'processing', 'analyzing', 'considering']):
                        thinking_messages.append(text)
        except NoSuchElementException:
            pass
        
        return thinking_messages
    
    def is_chatgpt_still_typing(self):
        """Check if ChatGPT is still generating a response"""
        if not self.driver:
            return False
            
        try:
            # Look for the stop generation button (indicates ChatGPT is still typing)
            stop_button = self.driver.find_elements(By.CSS_SELECTOR, 'button[aria-label="Stop generating"]')
            if stop_button and any(btn.is_displayed() for btn in stop_button):
                return True
                
            # Alternative: look for typing indicators
            typing_indicators = self.driver.find_elements(By.CSS_SELECTOR, 'div[class*="typing"], div[class*="generating"], div[class*="loading"]')
            if typing_indicators and any(ind.is_displayed() for ind in typing_indicators):
                return True
                
            # Look for thinking indicators
            thinking_messages = self.check_thinking_indicators()
            if thinking_messages:
                return True
                
        except Exception:
            pass
            
        return False
    
    def stream_response_with_updates(self, initial_message):
        """Stream the response with real-time updates"""
        self.current_streaming = True
        previous_text = self.last_response_text
        last_sent_text = ""
        timeout = time.time() + 120  # 2 minute timeout
        stable_count = 0
        displayed_thinking = set()
        update_counter = 0
        
        self.send_to_vscode("Starting response...", "stream_start") # Re-add stream_start
        
        while time.time() < timeout and self.current_streaming:
            # Check for thinking indicators
            thinking_messages = self.check_thinking_indicators()
            for message in thinking_messages:
                if message not in displayed_thinking:
                    self.send_to_vscode(f"[{message}]", "thinking")
                    displayed_thinking.add(message)
            
            current_text = self.get_latest_response_text()
            
            # Only update every few iterations to avoid too frequent updates
            update_counter += 1
            if update_counter % 3 == 0:  # Update every 3rd check
                # If we have significantly new content (more than just a few characters)
                if current_text and current_text != last_sent_text and current_text != previous_text:
                    if len(current_text) > len(last_sent_text) + 5:  # Only if meaningful new content
                        self.send_to_vscode(current_text, "stream_update") # Keep stream_update for now
                        last_sent_text = current_text
                        stable_count = 0
            
            # Check if ChatGPT is still typing
            if not self.is_chatgpt_still_typing():
                stable_count += 1
                # Wait for stability (no changes for a few checks)
                if stable_count >= 8:  # About 2.4 seconds of stability
                    break
            else:
                stable_count = 0
            
            previous_text = current_text
            time.sleep(0.3)  # Check every 300ms
        
        # Get the final complete response
        final_response = self.get_latest_response_text()
        
        # Send the final complete response
        if final_response:
            self.send_to_vscode(final_response, "assistant") # Send final as assistant message
        
        self.last_response_text = final_response
        self.current_streaming = False
        
        return final_response
    
    def send_message_to_chatgpt(self, message):
        """Send message to ChatGPT and get response"""
        if not self.driver or not self.initialized:
            self.send_to_vscode("ChatGPT not initialized yet. Please wait...", "error")
            return "ChatGPT not initialized yet. Please wait..."
        
        if self.current_streaming:
            self.send_to_vscode("Please wait for the current response to complete...", "error")
            return "Please wait for the current response to complete..."
        
        try:
            self.handle_popups()
            
            # Check if search is needed
            needs_search = self.should_use_search(message)
            if needs_search:
                self.send_to_vscode("[Search-related query detected]", "system")
            
            # Find input box and send message
            input_box = self.driver.find_element(By.CSS_SELECTOR, 'p[data-placeholder="Ask anything"]')
            input_box.click()
            time.sleep(0.5)
            
            # Clear and send message
            input_box.send_keys(Keys.CONTROL + "a")
            time.sleep(0.2)
            input_box.send_keys(Keys.BACKSPACE)
            time.sleep(0.2)
            input_box.send_keys(message)
            time.sleep(0.5)
            input_box.send_keys(Keys.ENTER)
            
            # Wait a moment for the response to start
            time.sleep(2)
            
            # Stream the response
            final_response = self.stream_response_with_updates(message)
            
            if final_response and len(final_response) > 10:
                return "Response completed successfully."
            else:
                self.send_to_vscode("No response received or response was too short. Please try again.", "error")
                return "No response received or response was too short. Please try again."
            
        except NoSuchElementException as e:
            error_msg = f"Could not find input box. Error: {str(e)}"
            self.send_to_vscode(error_msg, "error")
            return error_msg
        except Exception as e:
            error_msg = f"Error sending message: {str(e)}"
            self.send_to_vscode(error_msg, "error")
            return error_msg
    
    def process_messages(self):
        """Process messages from VS Code"""
        while self.running:
            try:
                if not self.message_queue.empty():
                    message = self.message_queue.get()
                    self.send_to_vscode(f"Processing: {message}", "system")
                    response = self.send_message_to_chatgpt(message)
                time.sleep(0.1)
            except Exception as e:
                self.send_to_vscode(f"Error processing message: {str(e)}", "error")
    
    def start(self):
        """Start the bridge"""
        self.send_to_vscode("Initializing ChatGPT Bridge...", "system")
        
        # Initialize driver in a separate thread
        init_thread = threading.Thread(target=self.initialize_driver)
        init_thread.daemon = True
        init_thread.start()
        
        # Start message processing thread
        processing_thread = threading.Thread(target=self.process_messages)
        processing_thread.daemon = True
        processing_thread.start()
        
        # Listen for messages from VS Code
        try:
            buffer = ""
            in_message = False
            current_message = ""

            for line in sys.stdin:
                line = line.rstrip('\n\r')

                if line == "<<<START_MESSAGE>>>":
                    in_message = True
                    current_message = ""
                elif line == "<<<END_MESSAGE>>>":
                    if in_message and current_message.strip():
                        message_to_process = current_message.strip()
                        if message_to_process.lower() not in ['exit', 'quit']:
                            self.message_queue.put(message_to_process)
                        elif message_to_process.lower() in ['exit', 'quit']:
                            break
                    in_message = False
                    current_message = ""
                elif in_message:
                    if current_message:
                        current_message += "\n" + line
                    else:
                        current_message = line
                else:
                    # Handle old format for backward compatibility
                    message = line.strip()
                    if message and message.lower() not in ['exit', 'quit']:
                        self.message_queue.put(message)
                    elif message.lower() in ['exit', 'quit']:
                        break
        except KeyboardInterrupt:
            self.running = False
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Clean up resources"""
        self.running = False
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass

if __name__ == "__main__":
    bridge = ChatGPTBridge()
    bridge.start()
